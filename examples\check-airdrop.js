/**
 * Airdrop Check Example
 * Demonstrates how to check airdrop eligibility and claim status for a specific TLD
 *
 * This example shows:
 * - Listing all airdrops for a TLD
 * - Checking airdrop details and status
 * - Finding claimable airdrops for addresses
 * - Verifying domain ownership and claim status
 *
 * Usage: node examples/check-airdrop.js
 */

// Load environment variables
require('dotenv').config();

const ODudeSDK = require('../src/index');
const { ethers } = require('ethers');

// ==================== CONFIGURATION VARIABLES ====================
// Update these variables to check different TLDs and addresses

const TLD_NAME = 'xxx'; // TLD to check airdrops for (matches test-airdrop.js)
const TOKEN_ADDRESS = '******************************************'; // Expected token address (USDC on Base Sepolia)

// Test addresses to check for airdrop eligibility
const TEST_ADDRESSES = [
  '******************************************', // Hardhat default account 1
  '******************************************', // Current wallet from .env
  '******************************************'  // Hardhat default account 3
];

// ==================== ERC20 TOKEN ABI ====================
const ERC20_ABI = [
  'function name() view returns (string)',
  'function symbol() view returns (string)',
  'function decimals() view returns (uint8)'
];

async function main() {
  console.log('=== ODude Airdrop Check Example ===\n');

  console.log('📋 Configuration:');
  console.log(`  TLD Name: ${TLD_NAME}`);
  console.log(`  Token Address: ${TOKEN_ADDRESS}`);
  console.log(`  Test Addresses: ${TEST_ADDRESSES.length} addresses`);
  console.log();

  // Initialize SDK with Base Sepolia (the working network)
  const sdk = new ODudeSDK({
    rpcUrl_sepolia: process.env.BASE_SEPOLIA_RPC_URL || 'https://sepolia.base.org'
  });

  try {
    sdk.connectNetwork('basesepolia');
    console.log('✅ Connected to Base Sepolia network');
  } catch (error) {
    console.log('❌ Failed to connect to Base Sepolia:', error.message);
    return;
  }

  // Get provider for token contract interactions
  const provider = sdk.getProvider('basesepolia');

  // Get token information
  let tokenDecimals = 18; // Default to 18 decimals
  let tokenName = 'Unknown';
  let tokenSymbol = 'UNK';

  try {
    const tokenContract = new ethers.Contract(TOKEN_ADDRESS, ERC20_ABI, provider);
    const [name, symbol, decimals] = await Promise.all([
      tokenContract.name(),
      tokenContract.symbol(),
      tokenContract.decimals()
    ]);

    tokenName = name;
    tokenSymbol = symbol;
    tokenDecimals = Number(decimals);

    console.log(`📱 Token Information:`);
    console.log(`  Name: ${tokenName}`);
    console.log(`  Symbol: ${tokenSymbol}`);
    console.log(`  Decimals: ${tokenDecimals}`);
    console.log();
  } catch (error) {
    console.log('⚠️  Could not fetch token information:', error.message);
    console.log('Using default decimals (18)');
    console.log();
  }

  console.log(`--- Airdrop Status for "${TLD_NAME}" TLD ---`);
  try {
    // Check airdrop count for the specified TLD
    const airdropCount = await sdk.rwairdrop().getTLDAirdropCount(TLD_NAME);
    console.log(`✓ Airdrop count for "${TLD_NAME}" TLD: ${airdropCount.toString()}`);

    if (airdropCount > 0) {
      // Get airdrop IDs for the TLD
      const airdropIds = await sdk.rwairdrop().getTLDAirdropIds(TLD_NAME);
      console.log(`✓ Airdrop IDs: [${airdropIds.map(id => id.toString()).join(', ')}]`);

      // Check all airdrop details
      for (let i = 0; i < airdropCount; i++) {
        const airdropInfo = await sdk.rwairdrop().getAirdropInfoByTLD(TLD_NAME, i);
        console.log(`\n📦 Airdrop ${i} Details:`);
        console.log(`  Token Address: ${airdropInfo.tokenAddress}`);
        console.log(`  Total Amount: ${ethers.formatUnits(airdropInfo.totalAmount, tokenDecimals)} ${tokenSymbol}`);
        console.log(`  Per User Share: ${ethers.formatUnits(airdropInfo.perUserShare, tokenDecimals)} ${tokenSymbol}`);
        console.log(`  Remaining Balance: ${ethers.formatUnits(airdropInfo.remainingBalance, tokenDecimals)} ${tokenSymbol}`);
        console.log(`  Is Active: ${airdropInfo.isActive ? '✅ Yes' : '❌ No'}`);
        console.log(`  Is Withdrawn: ${airdropInfo.isWithdrawn ? '✅ Yes' : '❌ No'}`);

        // Verify token address matches expected
        if (airdropInfo.tokenAddress.toLowerCase() === TOKEN_ADDRESS.toLowerCase()) {
          console.log(`  ✅ Token address matches expected (${tokenSymbol})`);
        } else {
          console.log(`  ⚠️  Token address mismatch! Expected: ${TOKEN_ADDRESS}, Got: ${airdropInfo.tokenAddress}`);
        }
      }
    } else {
      console.log(`❌ No airdrops found for "${TLD_NAME}" TLD`);
      console.log('💡 Create an airdrop using the test-airdrop.js example first');
    }
  } catch (error) {
    console.log('❌ Failed to get airdrop status:', error.message);
  }

  console.log('\n--- Checking Test Addresses ---');

  for (const address of TEST_ADDRESSES) {
    console.log(`\n👤 Address: ${address}`);

    try {
      // Get claimable airdrops for this user
      const claimableAirdrops = await sdk.rwairdrop().getClaimableAirdrops(address);
      console.log(`  📋 Claimable airdrops: ${claimableAirdrops.length}`);

      if (claimableAirdrops.length > 0) {
        claimableAirdrops.forEach((airdrop, index) => {
          console.log(`  🎁 Airdrop ${index + 1}:`);
          console.log(`    TLD: ${airdrop.tldName}`);
          console.log(`    Airdrop ID: ${airdrop.airdropId.toString()}`);
          console.log(`    Token: ${airdrop.tokenAddress}`);
          console.log(`    Per User Share: ${ethers.formatUnits(airdrop.perUserShare, tokenDecimals)} ${tokenSymbol}`);
          console.log(`    Can Claim: ${airdrop.canClaim ? '✅ Yes' : '❌ No'}`);
        });
      } else {
        console.log('  ❌ No claimable airdrops found');
      }

      // Check user domains in the specified TLD
      const userDomains = await sdk.rwairdrop().getUserDomainsInTLD(address, TLD_NAME);
      console.log(`  🏷️  Domains in '${TLD_NAME}' TLD: ${userDomains.length}`);
      if (userDomains.length > 0) {
        console.log(`    📝 Domains: ${userDomains.join(', ')}`);
      }
    } catch (error) {
      console.log('  ❌ Error:', error.message);
    }
  }

  console.log('\n--- Detailed Individual Check ---');

  const checkAddress = TEST_ADDRESSES[1]; // Use the wallet address from .env
  console.log(`\n🔍 Detailed check for: ${checkAddress}`);

  try {
    // Check if user has any domains that have claimed from airdrops in the specified TLD
    const userDomains = await sdk.rwairdrop().getUserDomainsInTLD(checkAddress, TLD_NAME);

    if (userDomains.length > 0) {
      console.log(`✅ User has ${userDomains.length} domains in '${TLD_NAME}' TLD:`);

      // Get airdrop count to check all airdrops
      const airdropCount = await sdk.rwairdrop().getTLDAirdropCount(TLD_NAME);

      for (const domain of userDomains) {
        console.log(`\n  🏷️  Domain: ${domain}`);

        // Check claim status for each airdrop
        for (let airdropIndex = 0; airdropIndex < airdropCount; airdropIndex++) {
          const hasClaimed = await sdk.rwairdrop().hasDomainClaimed(domain, airdropIndex);
          const claimedAmount = await sdk.rwairdrop().getDomainClaimedAmount(domain, airdropIndex);

          console.log(`    📦 Airdrop ${airdropIndex}:`);
          console.log(`      Has claimed: ${hasClaimed ? '✅ Yes' : '❌ No'}`);
          console.log(`      Claimed amount: ${ethers.formatUnits(claimedAmount, tokenDecimals)} ${tokenSymbol}`);
        }
      }

      console.log('\n💰 To claim from an airdrop, use:');
      console.log(`  const tx = await sdk.rwairdrop().claimShare("${TLD_NAME}", 0, "your-domain");`);
      console.log('  await tx.wait();');
    } else {
      console.log(`❌ User has no domains in "${TLD_NAME}" TLD`);
      console.log('💡 Register a domain in this TLD to be eligible for airdrops');
    }
  } catch (error) {
    console.log('❌ Error during detailed check:', error.message);
  }

  console.log('\n✓ Example completed!');
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Error:', error);
    process.exit(1);
  });

